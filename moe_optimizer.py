#!/usr/bin/env python3
# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""
MoE (Mixture of Experts) Optimization for Triton-free Operation

This script configures MoE models to work without Triton acceleration,
ensuring stable expert routing and load balancing on CUDA 7.5 hardware.
"""

import os
import sys
from typing import Dict, List, Tuple, Optional

class MoEOptimizer:
    """Configure MoE models for optimal performance without Triton kernels."""
    
    def __init__(self):
        # Qwen3-30B MoE specifications
        self.num_experts = 64        # Total experts in the model
        self.active_experts = 8      # Experts activated per token
        self.expert_hidden_size = 4096
        self.intermediate_size = 11008
        self.num_layers = 60
        
        # Hardware constraints
        self.num_gpus = 4
        self.gpu_memory_gb = 22.0
        
    def get_triton_free_environment(self) -> Dict[str, str]:
        """Get environment variables for Triton-free MoE operation."""
        return {
            # Core MoE Triton disabling
            'VLLM_DISABLED_KERNELS': 'TritonExperts,BatchedTritonExperts,TritonDeepGemmExperts,BatchedTritonOrDeepGemmExperts,TritonScaledMMLinearKernel',
            'VLLM_USE_DEEP_GEMM': '0',
            'VLLM_USE_TRITON_AWQ': '0',
            
            # MoE-specific optimizations
            'VLLM_FUSED_MOE_CHUNK_SIZE': '16384',  # Smaller chunks for stability
            'VLLM_MAX_TOKENS_PER_EXPERT_FP4_MOE': '81920',  # Reduced for memory efficiency
            'VLLM_MOE_ROUTING_SIMULATION_STRATEGY': '',  # Disable routing simulation
            
            # Force CUTLASS/CUDA backends
            'VLLM_USE_FLASHINFER_MOE_FP8': '0',
            'VLLM_USE_FLASHINFER_MOE_FP4': '0',
            'VLLM_USE_FLASHINFER_MOE_MXFP4_MXFP8': '0',
            'VLLM_USE_FLASHINFER_MOE_MXFP4_BF16': '0',
            
            # Memory optimization
            'PYTORCH_CUDA_ALLOC_CONF': 'max_split_size_mb:64,expandable_segments:True,roundup_power2_divisions:16',
        }
    
    def calculate_expert_memory_usage(self) -> Dict[str, float]:
        """Calculate memory usage for MoE experts."""
        # AWQ quantization: 4-bit weights
        weight_bits = 4
        
        # Memory per expert (gate + up + down projections)
        expert_params = (
            self.expert_hidden_size * self.intermediate_size +  # gate
            self.expert_hidden_size * self.intermediate_size +  # up  
            self.intermediate_size * self.expert_hidden_size    # down
        )
        
        # Memory in bytes (4-bit weights + fp16 scales)
        expert_weight_memory = (expert_params * weight_bits) / 8
        expert_scale_memory = (expert_params / 128) * 2  # fp16 scales every 128 elements
        expert_total_memory = expert_weight_memory + expert_scale_memory
        
        # Total for all experts across all layers
        total_expert_memory_gb = (expert_total_memory * self.num_experts * self.num_layers) / (1024**3)
        
        # Active expert memory during inference
        active_expert_memory_gb = (expert_total_memory * self.active_experts * self.num_layers) / (1024**3)
        
        return {
            'expert_params': expert_params,
            'expert_memory_bytes': expert_total_memory,
            'total_expert_memory_gb': total_expert_memory_gb,
            'active_expert_memory_gb': active_expert_memory_gb,
            'memory_per_gpu_gb': total_expert_memory_gb / self.num_gpus
        }
    
    def optimize_expert_parallelism(self) -> Dict[str, int]:
        """Calculate optimal expert parallelism settings."""
        memory_stats = self.calculate_expert_memory_usage()
        
        # Ensure experts fit in GPU memory
        experts_per_gpu = self.num_experts // self.num_gpus
        
        # Calculate optimal chunk size for expert processing
        # Smaller chunks reduce memory peaks but increase overhead
        base_chunk_size = 16384
        
        # Adjust based on available memory
        if memory_stats['memory_per_gpu_gb'] > 15:  # High memory usage
            chunk_size = base_chunk_size // 2
        elif memory_stats['memory_per_gpu_gb'] > 10:  # Medium memory usage
            chunk_size = base_chunk_size
        else:  # Low memory usage
            chunk_size = base_chunk_size * 2
        
        return {
            'experts_per_gpu': experts_per_gpu,
            'chunk_size': chunk_size,
            'max_tokens_per_expert': chunk_size * 5,  # Conservative multiplier
        }
    
    def get_moe_vllm_args(self) -> Dict[str, str]:
        """Get vLLM arguments optimized for MoE without Triton."""
        parallelism_settings = self.optimize_expert_parallelism()
        
        return {
            # Core MoE settings
            'tensor-parallel-size': str(self.num_gpus),
            'quantization': 'awq',
            'dtype': 'float16',
            
            # Memory optimization for MoE
            'gpu-memory-utilization': '0.75',  # Conservative for MoE
            'swap-space': '16',  # Extra swap for expert loading
            'enforce-eager': '',  # Disable CUDA graphs for stability
            
            # Batch size optimization
            'max-num-seqs': '4',  # Smaller batches for MoE stability
            'max-num-batched-tokens': str(parallelism_settings['chunk_size']),
            
            # Block size optimization
            'block-size': '16',  # Smaller blocks for better memory management
        }
    
    def verify_moe_compatibility(self) -> Tuple[bool, List[str]]:
        """Verify that MoE configuration is compatible with hardware."""
        issues = []
        memory_stats = self.calculate_expert_memory_usage()
        
        # Check memory requirements
        if memory_stats['memory_per_gpu_gb'] > 20:
            issues.append(f"Expert memory per GPU ({memory_stats['memory_per_gpu_gb']:.1f}GB) may exceed available memory")
        
        # Check expert distribution
        if self.num_experts % self.num_gpus != 0:
            issues.append(f"Number of experts ({self.num_experts}) not evenly divisible by GPUs ({self.num_gpus})")
        
        # Check environment variables
        env_vars = self.get_triton_free_environment()
        for var, expected in env_vars.items():
            actual = os.environ.get(var, '')
            if var == 'VLLM_DISABLED_KERNELS' and 'TritonExperts' not in actual:
                issues.append(f"Triton MoE kernels not properly disabled in {var}")
        
        return len(issues) == 0, issues
    
    def print_moe_analysis(self):
        """Print detailed MoE optimization analysis."""
        memory_stats = self.calculate_expert_memory_usage()
        parallelism_settings = self.optimize_expert_parallelism()
        compatible, issues = self.verify_moe_compatibility()
        
        print("=" * 80)
        print("MoE OPTIMIZATION ANALYSIS FOR TRITON-FREE OPERATION")
        print("=" * 80)
        print(f"Model: Qwen3-30B MoE ({self.num_experts} experts, {self.active_experts} active)")
        print(f"Hardware: {self.num_gpus}x RTX 2080 Ti ({self.gpu_memory_gb}GB each)")
        print()
        
        print("Expert Memory Analysis:")
        print(f"  Parameters per Expert:   {memory_stats['expert_params']:,}")
        print(f"  Memory per Expert:       {memory_stats['expert_memory_bytes']/1024**2:.1f} MB")
        print(f"  Total Expert Memory:     {memory_stats['total_expert_memory_gb']:.1f} GB")
        print(f"  Active Expert Memory:    {memory_stats['active_expert_memory_gb']:.1f} GB")
        print(f"  Memory per GPU:          {memory_stats['memory_per_gpu_gb']:.1f} GB")
        print()
        
        print("Expert Parallelism Settings:")
        print(f"  Experts per GPU:         {parallelism_settings['experts_per_gpu']}")
        print(f"  Chunk Size:              {parallelism_settings['chunk_size']:,}")
        print(f"  Max Tokens per Expert:   {parallelism_settings['max_tokens_per_expert']:,}")
        print()
        
        print("Triton-Free Configuration:")
        env_vars = self.get_triton_free_environment()
        for var, value in env_vars.items():
            print(f"  {var}={value}")
        print()
        
        print("Compatibility Check:")
        if compatible:
            print("✓ MoE configuration is compatible with hardware")
        else:
            print("✗ MoE configuration issues detected:")
            for issue in issues:
                print(f"  - {issue}")
        
        print("=" * 80)

def main():
    """Run MoE optimization analysis."""
    optimizer = MoEOptimizer()
    
    # Print analysis
    optimizer.print_moe_analysis()
    
    # Generate vLLM arguments
    args = optimizer.get_moe_vllm_args()
    print("Recommended vLLM arguments for MoE:")
    for key, value in args.items():
        if value:
            print(f"  --{key}={value}")
        else:
            print(f"  --{key}")
    print()
    
    # Environment variables
    env_vars = optimizer.get_triton_free_environment()
    print("Required environment variables:")
    for var, value in env_vars.items():
        print(f"export {var}=\"{value}\"")

if __name__ == "__main__":
    main()
