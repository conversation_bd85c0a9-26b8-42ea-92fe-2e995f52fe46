#!/usr/bin/env python3
# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""
Comprehensive Configuration Validation Script

This script validates the complete Triton bypass configuration and
provides recommendations for optimal long context inference.
"""

import os
import sys
import subprocess
import j<PERSON>
from typing import Dict, List, Tuple, Optional

def run_validation_script(script_path: str) -> <PERSON>ple[bool, str]:
    """Run a validation script and return success status and output."""
    try:
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, text=True, timeout=60)
        return result.returncode == 0, result.stdout + result.stderr
    except subprocess.TimeoutExpired:
        return False, "Script execution timed out"
    except Exception as e:
        return False, f"Error running script: {e}"

def check_file_exists(file_path: str) -> bool:
    """Check if a file exists."""
    return os.path.isfile(file_path)

def validate_run_script() -> <PERSON><PERSON>[bool, List[str]]:
    """Validate the run.sh script configuration."""
    issues = []
    
    if not check_file_exists("run.sh"):
        issues.append("run.sh script not found")
        return False, issues
    
    # Read and validate run.sh content
    try:
        with open("run.sh", "r") as f:
            content = f.read()
        
        # Check for required environment variables
        required_vars = [
            "VLLM_USE_TRITON_FLASH_ATTN=0",
            "VLLM_USE_TRITON_AWQ=0", 
            "VLLM_ATTENTION_BACKEND=XFORMERS",
            "VLLM_DISABLED_KERNELS=",
            "VLLM_USE_DEEP_GEMM=0"
        ]
        
        for var in required_vars:
            if var not in content:
                issues.append(f"Missing or incorrect: {var}")
        
        # Check for optimized vLLM arguments
        required_args = [
            "--max-model-len 65536",
            "--tensor-parallel-size=4",
            "--enforce-eager",
            "--gpu-memory-utilization=0.80"
        ]
        
        for arg in required_args:
            if arg not in content:
                issues.append(f"Missing vLLM argument: {arg}")
        
    except Exception as e:
        issues.append(f"Error reading run.sh: {e}")
    
    return len(issues) == 0, issues

def validate_helper_scripts() -> Tuple[bool, List[str]]:
    """Validate that all helper scripts are present."""
    required_scripts = [
        "triton_bypass_check.py",
        "memory_optimizer.py", 
        "moe_optimizer.py",
        "test_context_lengths.py"
    ]
    
    missing_scripts = []
    for script in required_scripts:
        if not check_file_exists(script):
            missing_scripts.append(script)
    
    return len(missing_scripts) == 0, missing_scripts

def run_triton_bypass_check() -> Tuple[bool, str]:
    """Run the Triton bypass verification."""
    return run_validation_script("triton_bypass_check.py")

def run_memory_analysis() -> Tuple[bool, str]:
    """Run memory optimization analysis."""
    return run_validation_script("memory_optimizer.py")

def run_moe_analysis() -> Tuple[bool, str]:
    """Run MoE optimization analysis."""
    return run_validation_script("moe_optimizer.py")

def generate_final_recommendations() -> List[str]:
    """Generate final recommendations based on validation results."""
    recommendations = [
        "CONFIGURATION VALIDATION COMPLETE",
        "",
        "Next Steps:",
        "1. Execute: source run.sh (to start vLLM with optimized settings)",
        "2. Wait for vLLM to fully initialize (may take 2-3 minutes)",
        "3. Test API: curl http://localhost:8000/v1/models",
        "4. Run context tests: python test_context_lengths.py",
        "",
        "Expected Results:",
        "- Zero Triton compilation errors during startup",
        "- Stable inference with context lengths >32K tokens",
        "- Maximum context length: 65,536+ tokens (target: 98,304)",
        "- API responsiveness maintained during long context inference",
        "",
        "Monitoring Commands:",
        "- GPU memory: nvidia-smi -l 1",
        "- vLLM logs: tail -f vllm.log",
        "- API health: curl http://localhost:8000/health",
        "",
        "Troubleshooting:",
        "- If Triton errors occur: Check environment variables",
        "- If OOM errors occur: Reduce --max-model-len or --gpu-memory-utilization",
        "- If slow inference: Check expert load balancing in logs"
    ]
    
    return recommendations

def main():
    """Run comprehensive configuration validation."""
    print("=" * 80)
    print("COMPREHENSIVE CONFIGURATION VALIDATION")
    print("=" * 80)
    
    all_passed = True
    
    # 1. Validate run.sh script
    print("1. Validating run.sh configuration...")
    run_valid, run_issues = validate_run_script()
    if run_valid:
        print("   ✓ run.sh configuration is correct")
    else:
        print("   ✗ run.sh configuration issues:")
        for issue in run_issues:
            print(f"     - {issue}")
        all_passed = False
    
    # 2. Validate helper scripts
    print("\n2. Validating helper scripts...")
    scripts_valid, missing_scripts = validate_helper_scripts()
    if scripts_valid:
        print("   ✓ All helper scripts are present")
    else:
        print("   ✗ Missing helper scripts:")
        for script in missing_scripts:
            print(f"     - {script}")
        all_passed = False
    
    # 3. Run Triton bypass check
    print("\n3. Running Triton bypass verification...")
    triton_valid, triton_output = run_triton_bypass_check()
    if triton_valid:
        print("   ✓ Triton bypass configuration is correct")
    else:
        print("   ✗ Triton bypass issues detected")
        print("   Output:")
        for line in triton_output.split('\n')[:10]:  # Show first 10 lines
            if line.strip():
                print(f"     {line}")
        all_passed = False
    
    # 4. Run memory analysis
    print("\n4. Running memory optimization analysis...")
    memory_valid, memory_output = run_memory_analysis()
    if memory_valid:
        print("   ✓ Memory optimization analysis completed")
    else:
        print("   ✗ Memory analysis issues")
        all_passed = False
    
    # 5. Run MoE analysis
    print("\n5. Running MoE optimization analysis...")
    moe_valid, moe_output = run_moe_analysis()
    if moe_valid:
        print("   ✓ MoE optimization analysis completed")
    else:
        print("   ✗ MoE analysis issues")
        all_passed = False
    
    # Final summary
    print("\n" + "=" * 80)
    if all_passed:
        print("✓ ALL VALIDATIONS PASSED")
        print("✓ Configuration is ready for long context inference")
    else:
        print("✗ SOME VALIDATIONS FAILED")
        print("✗ Please review and fix issues before proceeding")
    
    print("=" * 80)
    
    # Generate recommendations
    recommendations = generate_final_recommendations()
    for rec in recommendations:
        print(rec)
    
    # Save validation report
    report = {
        "validation_results": {
            "run_script_valid": run_valid,
            "helper_scripts_valid": scripts_valid,
            "triton_bypass_valid": triton_valid,
            "memory_analysis_valid": memory_valid,
            "moe_analysis_valid": moe_valid,
            "overall_valid": all_passed
        },
        "issues": {
            "run_script_issues": run_issues if not run_valid else [],
            "missing_scripts": missing_scripts if not scripts_valid else []
        },
        "recommendations": recommendations
    }
    
    with open("validation_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print(f"\nValidation report saved to: validation_report.json")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
