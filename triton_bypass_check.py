#!/usr/bin/env python3
# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""
Triton Bypass Verification Script for CUDA Compute Capability 7.5

This script verifies that all Triton kernels are properly disabled and that
vLLM will use only CUDA/XFormers backends for RTX 2080 Ti GPUs.
"""

import os
import sys
import torch
from typing import Optional, <PERSON><PERSON>

def check_cuda_capability() -> <PERSON><PERSON>[bool, str]:
    """Check if we're running on CUDA 7.5 hardware."""
    if not torch.cuda.is_available():
        return False, "CUDA not available"
    
    device_capability = torch.cuda.get_device_capability()
    capability_str = f"{device_capability[0]}.{device_capability[1]}"
    
    if device_capability == (7, 5):
        return True, f"CUDA Compute Capability {capability_str} (RTX 2080 Ti compatible)"
    else:
        return False, f"CUDA Compute Capability {capability_str} (not RTX 2080 Ti)"

def check_triton_disabled() -> <PERSON><PERSON>[bool, str]:
    """Verify that <PERSON><PERSON> is properly disabled."""
    try:
        from vllm.triton_utils.importing import HAS_TRITON
        if HAS_TRITON:
            return False, "Triton is still enabled - this may cause f16 compilation errors"
        else:
            return True, "Triton is properly disabled"
    except ImportError:
        return True, "Triton import failed (expected for bypass)"

def check_environment_variables() -> Tuple[bool, list]:
    """Check that all required environment variables are set correctly."""
    required_vars = {
        'VLLM_USE_TRITON_FLASH_ATTN': '0',
        'VLLM_USE_TRITON_AWQ': '0', 
        'VLLM_ATTENTION_BACKEND': 'XFORMERS',
        'VLLM_DISABLED_KERNELS': 'TritonScaledMMLinearKernel,TritonExperts,BatchedTritonExperts,TritonDeepGemmExperts,BatchedTritonOrDeepGemmExperts',
        'VLLM_USE_DEEP_GEMM': '0',
    }
    
    issues = []
    for var, expected in required_vars.items():
        actual = os.environ.get(var, '')
        if actual != expected:
            issues.append(f"{var}: expected '{expected}', got '{actual}'")
    
    return len(issues) == 0, issues

def check_attention_backend() -> Tuple[bool, str]:
    """Verify that XFormers backend is available and will be used."""
    try:
        from vllm.attention.layer import check_xformers_availability
        if check_xformers_availability():
            return True, "XFormers backend is available"
        else:
            return False, "XFormers backend is not available - may fall back to problematic backends"
    except ImportError:
        return False, "Cannot import XFormers availability check"

def check_memory_configuration() -> Tuple[bool, str]:
    """Check memory configuration for long context support."""
    pytorch_conf = os.environ.get('PYTORCH_CUDA_ALLOC_CONF', '')
    
    if 'max_split_size_mb' in pytorch_conf and 'expandable_segments' in pytorch_conf:
        return True, f"Memory configuration optimized: {pytorch_conf}"
    else:
        return False, f"Memory configuration may not be optimal: {pytorch_conf}"

def check_gpu_memory() -> Tuple[bool, str]:
    """Check available GPU memory."""
    if not torch.cuda.is_available():
        return False, "CUDA not available"
    
    total_memory = 0
    gpu_info = []
    
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        memory_gb = props.total_memory / (1024**3)
        total_memory += memory_gb
        gpu_info.append(f"GPU {i}: {props.name} ({memory_gb:.1f}GB)")
    
    if total_memory >= 80:  # 4x 22GB = 88GB
        return True, f"Sufficient GPU memory: {total_memory:.1f}GB total\n" + "\n".join(gpu_info)
    else:
        return False, f"Insufficient GPU memory: {total_memory:.1f}GB total\n" + "\n".join(gpu_info)

def main():
    """Run all checks and report results."""
    print("=" * 80)
    print("vLLM Triton Bypass Verification for CUDA Compute Capability 7.5")
    print("=" * 80)
    
    checks = [
        ("CUDA Capability", check_cuda_capability),
        ("Triton Disabled", check_triton_disabled),
        ("Environment Variables", check_environment_variables),
        ("XFormers Backend", check_attention_backend),
        ("Memory Configuration", check_memory_configuration),
        ("GPU Memory", check_gpu_memory),
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        try:
            passed, message = check_func()
            status = "✓ PASS" if passed else "✗ FAIL"
            print(f"{status:8} {check_name:20} {message}")
            
            if not passed:
                all_passed = False
                if isinstance(message, list):
                    for issue in message:
                        print(f"         {' ' * 20} - {issue}")
                        
        except Exception as e:
            print(f"✗ ERROR  {check_name:20} Exception: {e}")
            all_passed = False
    
    print("=" * 80)
    if all_passed:
        print("✓ ALL CHECKS PASSED - vLLM should run without Triton compilation errors")
        print("✓ Configuration optimized for long context sequences (>32K tokens)")
        return 0
    else:
        print("✗ SOME CHECKS FAILED - Please review configuration before starting vLLM")
        return 1

if __name__ == "__main__":
    sys.exit(main())
