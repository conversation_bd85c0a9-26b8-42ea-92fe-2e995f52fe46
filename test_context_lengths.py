#!/usr/bin/env python3
# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""
Context Length Testing Script for vLLM OpenAI API

This script tests vLLM's OpenAI-compatible API with progressively longer
context lengths to determine the maximum stable context length.
"""

import asyncio
import aiohttp
import time
import json
import sys
from typing import List, Dict, Optional, Tuple
import argparse

class ContextLengthTester:
    """Test vLLM API with different context lengths."""
    
    def __init__(self, base_url: str = "http://localhost:8000", model_name: str = "qwen3-30b"):
        self.base_url = base_url
        self.model_name = model_name
        self.api_url = f"{base_url}/v1/chat/completions"
        
    def generate_test_prompt(self, target_tokens: int) -> str:
        """Generate a test prompt with approximately target_tokens length."""
        # Approximate 4 characters per token for estimation
        chars_per_token = 4
        target_chars = target_tokens * chars_per_token
        
        # Base prompt
        base_prompt = """You are a helpful AI assistant. Please analyze the following text and provide a detailed summary.

Text to analyze:
"""
        
        # Filler content to reach target length
        filler_paragraph = """This is a sample paragraph that contains various information about artificial intelligence, machine learning, and natural language processing. It discusses the evolution of AI technologies, their applications in different industries, and the potential future developments in this rapidly advancing field. The paragraph also touches on the importance of ethical considerations in AI development and deployment."""
        
        # Calculate how much filler we need
        remaining_chars = target_chars - len(base_prompt) - 200  # Reserve space for completion
        
        if remaining_chars > 0:
            num_paragraphs = max(1, remaining_chars // len(filler_paragraph))
            filler_content = (filler_paragraph + "\n\n") * num_paragraphs
            full_prompt = base_prompt + filler_content[:remaining_chars]
        else:
            full_prompt = base_prompt
        
        return full_prompt
    
    async def test_single_context_length(self, session: aiohttp.ClientSession, 
                                       context_tokens: int, timeout: int = 300) -> Tuple[bool, Dict]:
        """Test a single context length."""
        prompt = self.generate_test_prompt(context_tokens)
        
        payload = {
            "model": self.model_name,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 100,  # Short response to focus on context processing
            "temperature": 0.1,
            "stream": False
        }
        
        start_time = time.time()
        
        try:
            async with session.post(
                self.api_url,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as response:
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status == 200:
                    result = await response.json()
                    
                    # Extract response details
                    usage = result.get('usage', {})
                    prompt_tokens = usage.get('prompt_tokens', 0)
                    completion_tokens = usage.get('completion_tokens', 0)
                    total_tokens = usage.get('total_tokens', 0)
                    
                    return True, {
                        'success': True,
                        'context_tokens': context_tokens,
                        'actual_prompt_tokens': prompt_tokens,
                        'completion_tokens': completion_tokens,
                        'total_tokens': total_tokens,
                        'response_time': response_time,
                        'status_code': response.status,
                        'error': None
                    }
                else:
                    error_text = await response.text()
                    return False, {
                        'success': False,
                        'context_tokens': context_tokens,
                        'response_time': response_time,
                        'status_code': response.status,
                        'error': error_text
                    }
                    
        except asyncio.TimeoutError:
            return False, {
                'success': False,
                'context_tokens': context_tokens,
                'response_time': timeout,
                'status_code': None,
                'error': 'Request timeout'
            }
        except Exception as e:
            return False, {
                'success': False,
                'context_tokens': context_tokens,
                'response_time': time.time() - start_time,
                'status_code': None,
                'error': str(e)
            }
    
    async def test_context_lengths(self, test_lengths: List[int]) -> List[Dict]:
        """Test multiple context lengths."""
        results = []
        
        async with aiohttp.ClientSession() as session:
            for context_length in test_lengths:
                print(f"Testing context length: {context_length:,} tokens...")
                
                success, result = await self.test_single_context_length(session, context_length)
                results.append(result)
                
                if success:
                    print(f"  ✓ Success - Response time: {result['response_time']:.2f}s")
                    print(f"    Actual tokens: {result['actual_prompt_tokens']:,} prompt + {result['completion_tokens']:,} completion")
                else:
                    print(f"  ✗ Failed - {result['error']}")
                    if result['status_code']:
                        print(f"    Status code: {result['status_code']}")
                
                # Wait between tests to avoid overwhelming the server
                await asyncio.sleep(2)
        
        return results
    
    def analyze_results(self, results: List[Dict]) -> Dict:
        """Analyze test results to determine maximum stable context length."""
        successful_tests = [r for r in results if r['success']]
        failed_tests = [r for r in results if not r['success']]
        
        if successful_tests:
            max_successful_length = max(r['context_tokens'] for r in successful_tests)
            avg_response_time = sum(r['response_time'] for r in successful_tests) / len(successful_tests)
        else:
            max_successful_length = 0
            avg_response_time = 0
        
        analysis = {
            'total_tests': len(results),
            'successful_tests': len(successful_tests),
            'failed_tests': len(failed_tests),
            'max_successful_length': max_successful_length,
            'avg_response_time': avg_response_time,
            'success_rate': len(successful_tests) / len(results) if results else 0
        }
        
        return analysis
    
    def print_summary(self, results: List[Dict], analysis: Dict):
        """Print test summary."""
        print("\n" + "=" * 80)
        print("CONTEXT LENGTH TEST SUMMARY")
        print("=" * 80)
        
        print(f"Total Tests:              {analysis['total_tests']}")
        print(f"Successful Tests:         {analysis['successful_tests']}")
        print(f"Failed Tests:             {analysis['failed_tests']}")
        print(f"Success Rate:             {analysis['success_rate']:.1%}")
        print(f"Max Successful Length:    {analysis['max_successful_length']:,} tokens")
        print(f"Average Response Time:    {analysis['avg_response_time']:.2f} seconds")
        print()
        
        print("Detailed Results:")
        for result in results:
            status = "✓" if result['success'] else "✗"
            print(f"  {status} {result['context_tokens']:>6,} tokens - {result['response_time']:>6.2f}s")
            if not result['success']:
                print(f"      Error: {result['error']}")
        
        print("=" * 80)

async def main():
    """Main test function."""
    parser = argparse.ArgumentParser(description="Test vLLM context lengths")
    parser.add_argument("--base-url", default="http://localhost:8000", help="vLLM API base URL")
    parser.add_argument("--model", default="qwen3-30b", help="Model name")
    parser.add_argument("--max-length", type=int, default=131072, help="Maximum context length to test")
    parser.add_argument("--step-size", type=int, default=16384, help="Step size for context length tests")
    
    args = parser.parse_args()
    
    # Generate test lengths
    test_lengths = []
    current_length = 1024  # Start with 1K
    while current_length <= args.max_length:
        test_lengths.append(current_length)
        if current_length < 32768:
            current_length *= 2  # Double until 32K
        else:
            current_length += args.step_size  # Linear steps after 32K
    
    print(f"Testing context lengths: {[f'{l:,}' for l in test_lengths]}")
    print(f"API URL: {args.base_url}")
    print(f"Model: {args.model}")
    print()
    
    # Run tests
    tester = ContextLengthTester(args.base_url, args.model)
    results = await tester.test_context_lengths(test_lengths)
    
    # Analyze and print results
    analysis = tester.analyze_results(results)
    tester.print_summary(results, analysis)
    
    # Save results to file
    output_file = f"context_test_results_{int(time.time())}.json"
    with open(output_file, 'w') as f:
        json.dump({
            'test_config': {
                'base_url': args.base_url,
                'model': args.model,
                'test_lengths': test_lengths
            },
            'results': results,
            'analysis': analysis
        }, f, indent=2)
    
    print(f"\nResults saved to: {output_file}")
    
    # Return appropriate exit code
    if analysis['max_successful_length'] >= 32768:
        print(f"\n✓ SUCCESS: Achieved target of >32K tokens ({analysis['max_successful_length']:,} tokens)")
        return 0
    else:
        print(f"\n✗ FAILED: Did not achieve target of >32K tokens (max: {analysis['max_successful_length']:,} tokens)")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
