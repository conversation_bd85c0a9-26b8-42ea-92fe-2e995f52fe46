#!/usr/bin/env python3
# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""
Memory Optimization Configuration for Long Context Sequences (>32K tokens)

This script calculates optimal memory settings for vLLM on RTX 2080 Ti GPUs
to support context lengths exceeding 32,768 tokens with Qwen3-30B AWQ MoE model.
"""

import math
import torch
from typing import Dict, Tuple, Optional

class MemoryOptimizer:
    """Calculate optimal memory settings for long context inference."""
    
    def __init__(self):
        # RTX 2080 Ti specifications
        self.gpu_memory_gb = 22.0  # GB per GPU
        self.num_gpus = 4
        self.total_gpu_memory_gb = self.gpu_memory_gb * self.num_gpus
        
        # Model specifications (Qwen3-30B AWQ)
        self.model_params = 30e9  # 30B parameters
        self.hidden_size = 4096   # Typical for 30B models
        self.num_layers = 60      # Typical for 30B models
        self.num_attention_heads = 32
        self.num_kv_heads = 8     # GQA ratio
        self.head_dim = self.hidden_size // self.num_attention_heads
        
        # AWQ quantization (4-bit weights)
        self.weight_bits = 4
        self.activation_dtype_bytes = 2  # float16
        
    def calculate_model_memory_gb(self) -> float:
        """Calculate base model memory usage."""
        # AWQ: 4-bit weights + scales + zeros
        weight_memory = (self.model_params * self.weight_bits) / 8 / (1024**3)
        scale_memory = (self.model_params / 128 * 2) / (1024**3)  # fp16 scales
        total_model_memory = weight_memory + scale_memory
        return total_model_memory
    
    def calculate_kv_cache_memory_per_token(self) -> float:
        """Calculate KV cache memory per token in bytes."""
        # K and V tensors: [num_layers, num_kv_heads, head_dim] * 2 (K+V) * dtype_bytes
        kv_memory_per_token = (
            self.num_layers * 
            self.num_kv_heads * 
            self.head_dim * 
            2 *  # K and V
            self.activation_dtype_bytes
        )
        return kv_memory_per_token
    
    def calculate_max_context_length(self, gpu_memory_utilization: float = 0.80) -> Dict[str, float]:
        """Calculate maximum supportable context length."""
        # Available memory per GPU
        available_memory_gb = self.gpu_memory_gb * gpu_memory_utilization
        total_available_gb = available_memory_gb * self.num_gpus
        
        # Model memory (distributed across GPUs)
        model_memory_gb = self.calculate_model_memory_gb()
        
        # Reserve memory for activations, gradients, and overhead
        activation_overhead_gb = 4.0  # Conservative estimate
        
        # Available memory for KV cache
        kv_cache_budget_gb = total_available_gb - model_memory_gb - activation_overhead_gb
        kv_cache_budget_bytes = kv_cache_budget_gb * (1024**3)
        
        # KV cache memory per token
        kv_memory_per_token = self.calculate_kv_cache_memory_per_token()
        
        # Maximum context length
        max_context_length = int(kv_cache_budget_bytes / kv_memory_per_token)
        
        return {
            'total_gpu_memory_gb': self.total_gpu_memory_gb,
            'available_memory_gb': total_available_gb,
            'model_memory_gb': model_memory_gb,
            'activation_overhead_gb': activation_overhead_gb,
            'kv_cache_budget_gb': kv_cache_budget_gb,
            'kv_memory_per_token_bytes': kv_memory_per_token,
            'max_context_length': max_context_length,
            'gpu_memory_utilization': gpu_memory_utilization
        }
    
    def optimize_block_size(self, target_context_length: int) -> int:
        """Calculate optimal block size for target context length."""
        # Block sizes must be powers of 2, typically 16, 32, 64, 128
        # Smaller blocks = more flexibility, larger blocks = better efficiency
        
        if target_context_length <= 32768:
            return 16  # Fine-grained for shorter contexts
        elif target_context_length <= 65536:
            return 32  # Balanced for medium contexts
        else:
            return 64  # Coarser for very long contexts
    
    def calculate_optimal_batch_settings(self, target_context_length: int) -> Dict[str, int]:
        """Calculate optimal batch size and sequence settings."""
        memory_stats = self.calculate_max_context_length()
        
        # Conservative batch size for long contexts
        if target_context_length <= 32768:
            max_num_seqs = 8
            max_num_batched_tokens = 32768
        elif target_context_length <= 65536:
            max_num_seqs = 4
            max_num_batched_tokens = 16384
        else:
            max_num_seqs = 2
            max_num_batched_tokens = 8192
        
        block_size = self.optimize_block_size(target_context_length)
        
        return {
            'max_num_seqs': max_num_seqs,
            'max_num_batched_tokens': max_num_batched_tokens,
            'block_size': block_size,
            'target_context_length': target_context_length
        }
    
    def generate_vllm_args(self, target_context_length: int) -> Dict[str, str]:
        """Generate optimized vLLM command line arguments."""
        memory_stats = self.calculate_max_context_length()
        batch_settings = self.calculate_optimal_batch_settings(target_context_length)
        
        # Ensure target doesn't exceed maximum
        safe_context_length = min(target_context_length, int(memory_stats['max_context_length'] * 0.9))
        
        # Calculate swap space (10% of total memory)
        swap_space_gb = int(self.total_gpu_memory_gb * 0.1)
        
        args = {
            'max-model-len': str(safe_context_length),
            'gpu-memory-utilization': '0.80',
            'swap-space': str(swap_space_gb),
            'block-size': str(batch_settings['block_size']),
            'max-num-seqs': str(batch_settings['max_num_seqs']),
            'max-num-batched-tokens': str(batch_settings['max_num_batched_tokens']),
            'tensor-parallel-size': str(self.num_gpus),
            'dtype': 'float16',
            'quantization': 'awq',
            'enforce-eager': '',  # No value needed for boolean flags
            'disable-sliding-window': '',
            'kv-cache-dtype': 'auto'
        }
        
        return args
    
    def print_memory_analysis(self, target_context_length: int):
        """Print detailed memory analysis."""
        memory_stats = self.calculate_max_context_length()
        batch_settings = self.calculate_optimal_batch_settings(target_context_length)
        
        print("=" * 80)
        print("MEMORY OPTIMIZATION ANALYSIS FOR LONG CONTEXT INFERENCE")
        print("=" * 80)
        print(f"Hardware: {self.num_gpus}x RTX 2080 Ti ({self.gpu_memory_gb}GB each)")
        print(f"Model: Qwen3-30B AWQ ({self.model_params/1e9:.0f}B parameters)")
        print(f"Target Context Length: {target_context_length:,} tokens")
        print()
        
        print("Memory Breakdown:")
        print(f"  Total GPU Memory:        {memory_stats['total_gpu_memory_gb']:.1f} GB")
        print(f"  Available Memory:        {memory_stats['available_memory_gb']:.1f} GB")
        print(f"  Model Memory:            {memory_stats['model_memory_gb']:.1f} GB")
        print(f"  Activation Overhead:     {memory_stats['activation_overhead_gb']:.1f} GB")
        print(f"  KV Cache Budget:         {memory_stats['kv_cache_budget_gb']:.1f} GB")
        print()
        
        print("KV Cache Analysis:")
        print(f"  Memory per Token:        {memory_stats['kv_memory_per_token_bytes']:,.0f} bytes")
        print(f"  Maximum Context Length:  {memory_stats['max_context_length']:,} tokens")
        print()
        
        print("Optimized Settings:")
        print(f"  Block Size:              {batch_settings['block_size']}")
        print(f"  Max Sequences:           {batch_settings['max_num_seqs']}")
        print(f"  Max Batched Tokens:      {batch_settings['max_num_batched_tokens']:,}")
        print()
        
        if target_context_length > memory_stats['max_context_length']:
            print("⚠️  WARNING: Target context length exceeds estimated maximum!")
            print(f"   Recommended maximum: {int(memory_stats['max_context_length'] * 0.9):,} tokens")
        else:
            print("✓ Target context length is within estimated limits")
        
        print("=" * 80)

def main():
    """Run memory optimization analysis."""
    optimizer = MemoryOptimizer()
    
    # Test different context lengths
    test_lengths = [32768, 65536, 98304, 131072]  # 32K, 64K, 96K, 128K
    
    for length in test_lengths:
        optimizer.print_memory_analysis(length)
        print()
        
        # Generate vLLM arguments
        args = optimizer.generate_vllm_args(length)
        print(f"Recommended vLLM arguments for {length:,} tokens:")
        for key, value in args.items():
            if value:
                print(f"  --{key}={value}")
            else:
                print(f"  --{key}")
        print()

if __name__ == "__main__":
    main()
